import { But<PERSON> } from "@/components/ui/button";
import Icon from "@/components/icon";
import { Link } from "@/i18n/navigation";

interface CTAButtonProps {
  title: string;
  url: string;
  target?: string;
  variant?:
    | "default"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | "destructive";
  icon?: string;
  className?: string;
}

export default function CTAButton({
  title,
  url,
  target = "",
  variant = "default",
  icon,
  className = "",
}: CTAButtonProps) {
  const isPrimary = variant === "default" || !variant;

  return (
    <Link
      href={url as any}
      target={target}
      className="group flex items-center justify-center"
    >
      <Button
        className={`
          relative overflow-hidden px-8 py-6 text-lg font-semibold
          transition-all duration-300 ease-out rounded-full
          ${
            isPrimary
              ? "bg-gradient-to-r from-red-500 via-orange-500 to-red-600 animate-gradient-x hover:animate-none hover:from-red-400 hover:via-orange-400 hover:to-red-500 shadow-lg shadow-red-500/30 hover:shadow-xl hover:shadow-red-500/50 text-white border-0"
              : "border-2 border-red-300/30 hover:border-red-400/50 bg-background/50 hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50"
          }
          hover:scale-105 hover:-translate-y-1
          active:scale-95 active:translate-y-0
          min-w-[200px] sm:min-w-[220px]
          ${className}
        `}
        size="lg"
        variant={variant}
      >
        {isPrimary && (
          <>
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
            <div className="absolute inset-0 bg-gradient-to-r from-red-400/20 via-orange-400/20 to-red-400/20 animate-pulse" />
          </>
        )}

        <div className="relative flex items-center gap-3 z-10">
          {icon && (
            <Icon
              name={icon}
              className="size-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12"
            />
          )}
          <span className="relative z-10 font-bold tracking-wide">{title}</span>
        </div>

        {isPrimary && (
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-red-500/30 via-orange-500/30 to-red-500/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
        )}
      </Button>
    </Link>
  );
}
